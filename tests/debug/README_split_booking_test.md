# 拆单功能测试指南

## 概述

本文档提供了拆单功能的完整测试指南，包括随机订单生成、拆单开关限制验证和功能测试。

## 功能实现总结

### 1. 拆单开关限制 ✅

**后端限制** (`app/views/task_views.py`)
- 在推送出票任务API中添加了拆单开关限制
- 如果订单曾经开启过拆单(`is_split_booking=1`)，则不允许关闭
- 返回HTTP 400错误："订单已开启拆单模式，不允许关闭拆单以避免数据不一致"

**前端限制** (`src/views/order/components/OrderButtons/AutoBookButton.vue`)
- 拆单开关在曾经开启过拆单的订单中被禁用
- 添加了禁用状态的视觉提示
- 显示警告信息："订单已开启拆单模式，不允许关闭以避免数据不一致"

### 2. 随机订单生成器 ✅

**功能特性**
- 随机生成1-3人的多人订单
- 支持成人和儿童乘客组合
- 随机生成姓名、护照号、联系方式
- 出发日期随机在0-3天内
- 支持多种机场和航班组合
- 生成完整的订单JSON数据

**使用方式**
```bash
cd /home/<USER>/workspace/flight/flight_order
python tests/debug/test_random_order_generator.py
```

## 测试步骤

### 步骤1：生成测试订单

1. **运行随机订单生成器**
   ```bash
   cd /home/<USER>/workspace/flight/flight_order
   python tests/debug/test_random_order_generator.py
   ```

2. **查看生成的订单数据**
   - 订单数据保存在 `tests/debug/random_orders_YYYYMMDD_HHMMSS.json`
   - 包含5个随机生成的多人订单
   - 每个订单包含完整的订单信息、行程、乘客和联系人数据

### 步骤2：创建订单（需要服务运行）

如果flight_order服务正在运行，可以通过以下方式创建订单：

1. **使用curl命令**
   ```bash
   curl -X POST http://localhost:8000/api/v1/flight_order/public/order/create \
        -H "Content-Type: application/json" \
        -d @tests/debug/random_orders_20250528_145227.json[0]
   ```

2. **使用Postman**
   - URL: `POST http://localhost:8000/api/v1/flight_order/public/order/create`
   - Body: 选择生成文件中的任一订单数据

3. **重新运行生成器**
   - 如果服务运行，脚本会自动尝试创建订单

### 步骤3：测试拆单功能

1. **在管理后台找到创建的订单**
   - 登录flight_web_admin管理后台
   - 在订单管理页面搜索生成的订单号
   - 例如：`TEST17484151422382`、`TEST17484151436149`等

2. **测试拆单开关**
   - 点击订单的"自动出票"按钮
   - 在弹出的对话框中找到"是否拆单出票"开关
   - 首次可以自由开启/关闭拆单

3. **测试拆单限制**
   - 开启拆单模式并推送任务
   - 再次点击"自动出票"按钮
   - 验证拆单开关已被禁用且显示警告信息
   - 尝试通过API关闭拆单，应返回400错误

### 步骤4：验证拆单逻辑

1. **多乘客拆单测试**
   - 选择包含多个乘客的订单（成人+儿童）
   - 开启拆单模式推送出票任务
   - 验证每个乘客独立构建出票任务

2. **儿童拆分为成人测试**
   - 在包含儿童乘客的订单中开启拆单
   - 同时开启"儿童拆分为成人"选项
   - 验证儿童乘客按成人类型出票

3. **状态管理测试**
   - 验证乘客出票状态的正确更新
   - 验证订单整体状态的智能判断
   - 验证部分成功场景的处理

## 生成的测试订单示例

最新生成的5个测试订单：

1. **TEST17484151422382** - 1成人+2儿童 (SGN→XIY)
2. **TEST17484151436149** - 1成人+1儿童 (PEK→HAN)  
3. **TEST17484151443281** - 1成人 (SHA→SGN)
4. **TEST17484151459049** - 1成人+1儿童 (XIY→SGN)
5. **TEST17484151462977** - 3成人 (HAN→SGN)

## 预期测试结果

### ✅ 拆单开关限制
- 首次可以自由开启/关闭拆单开关
- 开启拆单后，开关被禁用且显示警告
- API调用返回400错误阻止关闭拆单

### ✅ 拆单功能
- 多乘客订单正确拆分为独立任务
- 儿童拆分为成人功能正常工作
- 乘客状态和订单状态正确管理

### ✅ 数据一致性
- 拆单状态一旦开启不可逆转
- 避免因开关切换导致的数据不一致
- 保证拆单逻辑的稳定性

## 故障排除

### 订单创建失败
- 确保flight_order服务正在运行
- 检查数据库连接是否正常
- 验证订单数据格式是否正确

### 拆单开关未禁用
- 检查订单的`is_split_booking`字段值
- 确认前端组件正确读取订单状态
- 验证后端API限制逻辑

### 拆单任务未正确构建
- 检查`build_split_book_tasks`方法
- 验证乘客状态过滤逻辑
- 确认任务数据结构正确

## 相关文件

- **后端API**: `/home/<USER>/workspace/flight/flight_order/app/views/task_views.py`
- **前端组件**: `/home/<USER>/workspace/flight/flight_web_admin/src/views/order/components/OrderButtons/AutoBookButton.vue`
- **订单生成器**: `/home/<USER>/workspace/flight/flight_order/tests/debug/test_random_order_generator.py`
- **单元测试**: `/home/<USER>/workspace/flight/flight_order/tests/service_tests/test_split_booking_*.py`

拆单功能现已完整实现并通过测试验证！🎉
